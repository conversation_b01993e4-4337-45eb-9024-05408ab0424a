import express, { Request, Response } from 'express';
import mongoose from 'mongoose';
import { ApexSession, ApexTeam, ApexKart, ApexDriver, ApexCompetitor, ApexLap, ApexPitStop, ApexLiveData } from '../models/ApexModels';
import ApexParser from '../services/apexParser';
import { ApexPitRowService } from '../services/apexPitRowService';
import fs from 'fs';
import path from 'path';

const router = express.Router();

// Helper function to find session by ObjectId or title
async function findSessionByIdOrTitle(sessionIdentifier: string) {
  // First try to find by ObjectId
  if (mongoose.Types.ObjectId.isValid(sessionIdentifier)) {
    const session = await ApexSession.findById(sessionIdentifier);
    if (session) return session;
  }

  // If not found or not a valid ObjectId, try to find by title combination
  const sessions = await ApexSession.find({
    $or: [
      { title1: { $regex: sessionIdentifier, $options: 'i' } },
      { title2: { $regex: sessionIdentifier, $options: 'i' } },
      { $expr: {
          $regexMatch: {
            input: { $concat: ['$title1', ' - ', '$title2'] },
            regex: sessionIdentifier,
            options: 'i'
          }
        }
      }
    ]
  }).sort({ createdAt: -1 });

  return sessions.length > 0 ? sessions[0] : null;
}

// Parse log file endpoint
router.post('/parse-log', async (req: Request, res: Response) => {
  try {
    const { logContent, sessionId, raceId } = req.body;

    if (!logContent) {
      res.status(400).json({ error: 'Log content is required' });
      return;
    }

    const parser = new ApexParser({
      sessionId,
      raceId,
      enableLogging: true
    });

    await parser.parseLogFile(logContent);

    const session = parser.getCurrentSession();
    
    res.json({
      success: true,
      message: 'Log file parsed successfully',
      session: session ? {
        sessionId: session._id,
        raceId: session.raceId,
        title1: session.title1,
        title2: session.title2,
        track: session.track
      } : null
    });

  } catch (error) {
    console.error('Error parsing log file:', error);
    res.status(500).json({ error: 'Failed to parse log file' });
  }
});

// Parse log file using the simplified apex parser
router.post('/parse-log-file-simple', async (req: Request, res: Response) => {
  try {
    const { filePath, sessionId, raceId } = req.body;

    if (!filePath) {
      res.status(400).json({ error: 'File path is required' });
      return;
    }

    const fullPath = path.join(__dirname, '..', '..', 'apex parser files', filePath);

    if (!fs.existsSync(fullPath)) {
      res.status(404).json({ error: `File not found: ${filePath}` });
      return;
    }

    // Read file content
    const content = fs.readFileSync(fullPath, 'utf8');

    // Create simplified parser with config
    const { ApexParserSimple } = await import('../services/apexParserSimple');
    const parser = new ApexParserSimple({
      sessionId,
      raceId: raceId || `race_${filePath.replace('.txt', '')}_${Date.now()}`,
      enableLogging: true
    });

    // Parse the content
    await parser.parseLogContent(content);

    const session = parser.getCurrentSession();
    const gridData = parser.getGridData();

    // Get entity counts for response
    let entitiesCreated = null;
    if (session) {
      try {
        const [teamCount, kartCount, competitorCount] = await Promise.all([
          ApexTeam.countDocuments({ sessionId: session._id }),
          ApexKart.countDocuments({ sessionId: session._id }),
          ApexCompetitor.countDocuments({ sessionId: session._id })
        ]);
        entitiesCreated = { teamCount, kartCount, competitorCount };
      } catch (countError) {
        console.warn('Error counting entities:', countError);
      }
    }

    res.json({
      success: true,
      message: 'Log file parsed successfully using simplified parser',
      session: session ? {
        sessionId: session._id,
        raceId: session.raceId,
        title1: session.title1,
        title2: session.title2,
        track: session.track,
        startTime: session.startTime
      } : null,
      gridData: gridData ? {
        driversCount: Object.keys(gridData.drivers).length,
        headersCount: Object.keys(gridData.header_types).length
      } : null,
      entitiesCreated,
      filePath,
      contentLength: content.length
    });

  } catch (error) {
    console.error('Error parsing log file with simplified parser:', error);
    res.status(500).json({
      error: 'Failed to parse log file',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Parse log file from file system
router.post('/parse-log-file', async (req: Request, res: Response) => {
  try {
    const { filePath, sessionId, raceId } = req.body;

    if (!filePath) {
      res.status(400).json({ error: 'File path is required' });
      return;
    }

    // Read file from apex parser files directory
    const fullPath = path.join(__dirname, '../../apex parser files', filePath);
    
    if (!fs.existsSync(fullPath)) {
      res.status(404).json({ error: 'Log file not found' });
      return;
    }

    const logContent = fs.readFileSync(fullPath, 'utf8');

    const parser = new ApexParser({
      sessionId,
      raceId,
      enableLogging: true
    });

    await parser.parseLogFile(logContent);

    const session = parser.getCurrentSession();
    
    res.json({
      success: true,
      message: 'Log file parsed successfully',
      session: session ? {
        sessionId: session._id,
        raceId: session.raceId,
        title1: session.title1,
        title2: session.title2,
        track: session.track
      } : null
    });

  } catch (error) {
    console.error('Error parsing log file:', error);
    res.status(500).json({ error: 'Failed to parse log file' });
  }
});

// Get all sessions
router.get('/sessions', async (req, res) => {
  try {
    const sessions = await ApexSession.find()
      .sort({ createdAt: -1 })
      .select('_id raceId title1 title2 track startTime isActive createdAt');

    res.json(sessions);
  } catch (error) {
    console.error('Error fetching sessions:', error);
    res.status(500).json({ error: 'Failed to fetch sessions' });
  }
});

// Get session by ID
router.get('/sessions/:sessionId', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      res.status(400).json({ error: 'Session ID is required' });
      return;
    }

    const session = await findSessionByIdOrTitle(sessionId);

    if (!session) {
      res.status(404).json({ error: 'Session not found' });
      return;
    }

    res.json(session);
  } catch (error) {
    console.error('Error fetching session:', error);
    res.status(500).json({ error: 'Failed to fetch session' });
  }
});

// Get teams for a session
router.get('/sessions/:sessionId/teams', async (req, res) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      res.status(400).json({ error: 'Session ID is required' });
      return;
    }

    const session = await findSessionByIdOrTitle(sessionId);
    if (!session) {
      res.status(404).json({ error: 'Session not found' });
      return;
    }

    const teams = await ApexTeam.find({ sessionId: session._id })
      .populate<{ currentKartId: any }>({
        path: 'currentKartId',
        select: 'number kartNumber status speed isActive'
      })
      .sort({ kartNumber: 1 });

    res.json(teams);
  } catch (error) {
    console.error('Error fetching teams:', error);
    res.status(500).json({ error: 'Failed to fetch teams' });
  }
});

// Get karts for a session
router.get('/sessions/:sessionId/karts', async (req, res) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      res.status(400).json({ error: 'Session ID is required' });
      return;
    }

    const session = await findSessionByIdOrTitle(sessionId);
    if (!session) {
      res.status(404).json({ error: 'Session not found' });
      return;
    }

    const karts = await ApexKart.find({ sessionId: session._id }).sort({ kartNumber: 1 });

    // Populate lap time information for each kart
    const kartsWithLapInfo = await Promise.all(
      karts.map(async (kart) => {
        // Get latest lap times for this kart
        const latestLap = await ApexLap.findOne({
          sessionId: session._id,
          kartNumber: kart.kartNumber
        }).sort({ timestamp: -1 });

        const bestLap = await ApexLap.findOne({
          sessionId: session._id,
          kartNumber: kart.kartNumber
        }).sort({ lapTime: 1 });

        const totalLaps = await ApexLap.countDocuments({
          sessionId: session._id,
          kartNumber: kart.kartNumber
        });

        // Update kart with lap information
        const kartObj = kart.toObject();
        kartObj.lastLapTimeFormatted = latestLap?.lapTimeFormatted || undefined;
        kartObj.bestLapTimeFormatted = bestLap?.lapTimeFormatted || undefined;
        kartObj.totalLaps = totalLaps;

        return kartObj;
      })
    );

    res.json(kartsWithLapInfo);
  } catch (error) {
    console.error('Error fetching karts:', error);
    res.status(500).json({ error: 'Failed to fetch karts' });
  }
});

// Get competitors for a session
router.get('/sessions/:sessionId/competitors', async (req, res) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      res.status(400).json({ error: 'Session ID is required' });
      return;
    }

    const session = await findSessionByIdOrTitle(sessionId);
    if (!session) {
      res.status(404).json({ error: 'Session not found' });
      return;
    }

    const competitors = await ApexCompetitor.find({ sessionId: session._id }).sort({ kartNumber: 1 });

    res.json(competitors);
  } catch (error) {
    console.error('Error fetching competitors:', error);
    res.status(500).json({ error: 'Failed to fetch competitors' });
  }
});

// Get laps for a session
router.get('/sessions/:sessionId/laps', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { competitorId, limit = 100 } = req.query;

    if (!sessionId) {
      res.status(400).json({ error: 'Session ID is required' });
      return;
    }

    const session = await findSessionByIdOrTitle(sessionId);
    if (!session) {
      res.status(404).json({ error: 'Session not found' });
      return;
    }

    let query: any = { sessionId: session._id };
    if (competitorId) {
      query.competitorId = competitorId;
    }

    const laps = await ApexLap.find(query)
      .sort({ timestamp: -1 })
      .limit(parseInt(limit as string))
      .lean();

    res.json(laps);
  } catch (error) {
    console.error('Error fetching laps:', error);
    res.status(500).json({ error: 'Failed to fetch laps' });
  }
});

// Get pit stops for a session
router.get('/sessions/:sessionId/pitstops', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { competitorId } = req.query;

    if (!sessionId) {
      res.status(400).json({ error: 'Session ID is required' });
      return;
    }

    const session = await findSessionByIdOrTitle(sessionId);
    if (!session) {
      res.status(404).json({ error: 'Session not found' });
      return;
    }

    let query: any = { sessionId: session._id };
    if (competitorId) {
      query.competitorId = competitorId;
    }

    const pitStops = await ApexPitStop.find(query)
      .sort({ pitInTime: -1 })
      .lean();

    res.json(pitStops);
  } catch (error) {
    console.error('Error fetching pit stops:', error);
    res.status(500).json({ error: 'Failed to fetch pit stops' });
  }
});

// Get live data for a session
router.get('/sessions/:sessionId/live', async (req, res) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      res.status(400).json({ error: 'Session ID is required' });
      return;
    }

    const session = await findSessionByIdOrTitle(sessionId);
    if (!session) {
      res.status(404).json({ error: 'Session not found' });
      return;
    }

    const liveData = await ApexLiveData.find({ sessionId: session._id })
      .sort({ position: 1 })
      .lean();

    res.json(liveData);
  } catch (error) {
    console.error('Error fetching live data:', error);
    res.status(500).json({ error: 'Failed to fetch live data' });
  }
});

// Get session statistics
router.get('/sessions/:sessionId/stats', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      res.status(400).json({ error: 'Session ID is required' });
      return;
    }

    const session = await findSessionByIdOrTitle(sessionId);
    if (!session) {
      res.status(404).json({ error: 'Session not found' });
      return;
    }

    const [teamCount, lapCount, pitStopCount] = await Promise.all([
      ApexTeam.countDocuments({ sessionId: session._id }),
      ApexLap.countDocuments({ sessionId: session._id }),
      ApexPitStop.countDocuments({ sessionId: session._id })
    ]);

    // Get fastest lap
    const fastestLap = await ApexLap.findOne({ sessionId: session._id })
      .sort({ lapTime: 1 })
      .populate('competitorId', 'name')
      .lean();

    res.json({
      session: {
        sessionId: session._id,
        raceId: session.raceId,
        title1: session.title1,
        title2: session.title2,
        track: session.track,
        startTime: session.startTime,
        isActive: session.isActive
      },
      statistics: {
        teamCount,
        lapCount,
        pitStopCount,
        fastestLap: fastestLap ? {
          competitorId: fastestLap.competitorId,
          lapTime: fastestLap.lapTime,
          lapTimeFormatted: fastestLap.lapTimeFormatted,
          timestamp: fastestLap.timestamp
        } : null
      }
    });

  } catch (error) {
    console.error('Error fetching session statistics:', error);
    res.status(500).json({ error: 'Failed to fetch session statistics' });
  }
});

// Delete session and all related data
router.delete('/sessions/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      res.status(400).json({ error: 'Session ID is required' });
      return;
    }

    const session = await findSessionByIdOrTitle(sessionId);
    if (!session) {
      res.status(404).json({ error: 'Session not found' });
      return;
    }

    // Delete all related data using the session's ObjectId
    await Promise.all([
      ApexSession.findByIdAndDelete(session._id),
      ApexTeam.deleteMany({ sessionId: session._id }),
      ApexKart.deleteMany({ sessionId: session._id }),
      ApexDriver.deleteMany({ sessionId: session._id }),
      ApexCompetitor.deleteMany({ sessionId: session._id }),
      ApexLap.deleteMany({ sessionId: session._id }),
      ApexPitStop.deleteMany({ sessionId: session._id }),
      ApexLiveData.deleteMany({ sessionId: session._id })
    ]);

    res.json({ success: true, message: 'Session and all related data deleted successfully' });

  } catch (error) {
    console.error('Error deleting session:', error);
    res.status(500).json({ error: 'Failed to delete session' });
  }
});

// Get available log files
router.get('/log-files', async (_req: Request, res: Response) => {
  try {
    const logFilesDir = path.join(__dirname, '../../apex parser files');
    
    if (!fs.existsSync(logFilesDir)) {
      res.json([]);
      return;
    }

    const files = fs.readdirSync(logFilesDir)
      .filter(file => file.endsWith('.txt'))
      .map(file => {
        const filePath = path.join(logFilesDir, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          size: stats.size,
          modified: stats.mtime
        };
      })
      .sort((a, b) => b.modified.getTime() - a.modified.getTime());

    res.json(files);

  } catch (error) {
    console.error('Error fetching log files:', error);
    res.status(500).json({ error: 'Failed to fetch log files' });
  }
});

// Debug endpoint to check team creation and competitor mapping
router.get('/sessions/:sessionId/debug-teams', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      res.status(400).json({ error: 'Session ID is required' });
      return;
    }

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(sessionId)) {
      res.status(400).json({ error: 'Invalid session ID format' });
      return;
    }

    // Get all teams, karts, and competitors for this session
    const [teams, karts, competitors] = await Promise.all([
      ApexTeam.find({ sessionId: new mongoose.Types.ObjectId(sessionId) }).lean(),
      ApexKart.find({ sessionId: new mongoose.Types.ObjectId(sessionId) }).lean(),
      ApexCompetitor.find({ sessionId: new mongoose.Types.ObjectId(sessionId) }).lean()
    ]);

    res.json({
      sessionId,
      counts: {
        teams: teams.length,
        karts: karts.length,
        competitors: competitors.length
      },
      teams: teams.map(t => ({
        _id: t._id,
        teamId: t.teamId,
        name: t.name,
        number: t.number,
        kartNumber: t.kartNumber
      })),
      competitors: competitors.map(c => ({
        _id: c._id,
        competitorId: c.competitorId,
        teamId: c.teamId,
        name: c.name,
        kartNumber: c.kartNumber
      })),
      karts: karts.map(k => ({
        _id: k._id,
        number: k.number,
        kartNumber: k.kartNumber,
        teamId: k.teamId
      }))
    });

  } catch (error) {
    console.error('Error debugging teams:', error);
    res.status(500).json({
      error: 'Failed to debug teams',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Create apex karts for existing pit rows
router.post('/sessions/:sessionId/create-pit-row-karts', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      res.status(400).json({ error: 'Session ID is required' });
      return;
    }

    await ApexPitRowService.createApexKartsForPitRows(sessionId);

    res.json({
      success: true,
      message: 'Apex karts created for pit rows successfully'
    });

  } catch (error) {
    console.error('Error creating apex karts for pit rows:', error);
    res.status(500).json({
      error: 'Failed to create apex karts for pit rows',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Validate pit rows for apex mode
router.get('/sessions/:sessionId/validate-pit-rows', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      res.status(400).json({ error: 'Session ID is required' });
      return;
    }

    const validation = await ApexPitRowService.validatePitRowsForApexMode(sessionId);

    res.json(validation);

  } catch (error) {
    console.error('Error validating pit rows for apex mode:', error);
    res.status(500).json({
      error: 'Failed to validate pit rows',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Update apex kart properties (speed, status, etc.)
router.put('/sessions/:sessionId/karts/:kartId', async (req: Request, res: Response) => {
  try {
    const { sessionId, kartId } = req.params;
    const updateData = req.body;

    if (!sessionId || !kartId) {
      res.status(400).json({ error: 'Session ID and kart ID are required' });
      return;
    }

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(kartId)) {
      res.status(400).json({ error: 'Invalid kart ID format' });
      return;
    }

    // Find and update the apex kart
    const updatedKart = await ApexKart.findOneAndUpdate(
      {
        sessionId: new mongoose.Types.ObjectId(sessionId),
        _id: new mongoose.Types.ObjectId(kartId)
      },
      {
        ...updateData,
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    );

    if (!updatedKart) {
      res.status(404).json({ error: `Apex kart ${kartId} not found in session ${sessionId}` });
      return;
    }

    res.json(updatedKart);

  } catch (error) {
    console.error('Error updating apex kart:', error);
    res.status(500).json({
      error: 'Failed to update apex kart',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get lap times for a specific kart
router.get('/sessions/:sessionId/karts/:kartNumber/laps', async (req: Request, res: Response) => {
  try {
    const { sessionId, kartNumber } = req.params;

    if (!sessionId || !kartNumber) {
      res.status(400).json({ error: 'Session ID and kart number are required' });
      return;
    }

    // Find the kart first to get its team ID
    const kart = await ApexKart.findOne({
      sessionId: new mongoose.Types.ObjectId(sessionId),
      kartNumber: parseInt(kartNumber)
    });

    if (!kart) {
      res.status(404).json({ error: `Kart ${kartNumber} not found in session ${sessionId}` });
      return;
    }

    // Get lap times for this kart - try both by kartNumber and teamId
    const laps = await ApexLap.find({
      sessionId: new mongoose.Types.ObjectId(sessionId),
      $or: [
        { kartNumber: parseInt(kartNumber) },
        { teamId: kart.teamId }
      ]
    })
    .sort({ lapNumber: -1 }) // Most recent first
    .limit(20) // Limit to last 20 laps
    .select('lapNumber lapTime lapTimeFormatted timestamp');

    res.json(laps);

  } catch (error) {
    console.error('Error fetching kart lap times:', error);
    res.status(500).json({
      error: 'Failed to fetch kart lap times',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
