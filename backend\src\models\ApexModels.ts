import mongoose, { Document, Schema } from 'mongoose';

// Apex Session Model
export interface IApexSession extends Document {
  raceId: string;
  title1: string;
  title2: string;
  track: string;
  startTime: Date;
  endTime?: Date;
  isActive: boolean;
  gridData?: any;
  sessionData?: Record<string, any>;
  lastUpdated: Date;
  createdAt: Date;
  updatedAt: Date;
}

const ApexSessionSchema = new Schema<IApexSession>({
  raceId: { type: String, required: true, index: true },
  title1: { type: String, default: '' },
  title2: { type: String, default: '' },
  track: { type: String, default: '' },
  startTime: { type: Date, required: true },
  endTime: { type: Date },
  isActive: { type: Boolean, default: true, index: true },
  gridData: { type: Schema.Types.Mixed },
  sessionData: { type: Schema.Types.Mixed, default: {} },
  lastUpdated: { type: Date, default: Date.now }
}, {
  timestamps: true,
  collection: 'apex_sessions'
});

// Apex Team Model
export interface IApexTeam extends Document {
  sessionId: mongoose.Types.ObjectId;
  teamId: string;
  name: string;
  number: number; // Align with regular Team.number
  kartNumber: number;
  currentKartId?: mongoose.Types.ObjectId; // Align with regular Team.currentKartId (ObjectId ref)
  pastKarts: mongoose.Types.ObjectId[]; // Align with regular Team.pastKarts
  pits: mongoose.Types.ObjectId[]; // Align with regular Team.pits
  drivers: string[];
  nationality: string;
  currentDriverId?: string;
  totalLaps: number;
  bestLapTime?: number;
  bestLapTimeFormatted?: string; // Align naming
  lastLapTime?: number;
  lastLapTimeFormatted?: string; // Align naming
  position?: number;
  status: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const ApexTeamSchema = new Schema<IApexTeam>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  teamId: { type: String, required: true, index: true },
  name: { type: String, required: true },
  number: { type: Number, required: true, index: true }, // Align with regular Team.number
  kartNumber: { type: Number, required: true, index: true },
  currentKartId: { type: Schema.Types.ObjectId, ref: 'ApexKart', default: null }, // Align with regular Team.currentKartId
  pastKarts: [{ type: Schema.Types.ObjectId, ref: 'ApexKart' }], // Align with regular Team.pastKarts
  pits: [{ type: Schema.Types.ObjectId, ref: 'Pit' }], // Align with regular Team.pits
  drivers: [{ type: String }],
  nationality: { type: String, default: 'Unknown' },
  currentDriverId: { type: String },
  totalLaps: { type: Number, default: 0 },
  bestLapTime: { type: Number },
  bestLapTimeFormatted: { type: String }, // Align naming
  lastLapTime: { type: Number },
  lastLapTimeFormatted: { type: String }, // Align naming
  position: { type: Number },
  status: { type: String, default: 'active' },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_teams'
});

// Compound index for session and team
ApexTeamSchema.index({ sessionId: 1, teamId: 1 }, { unique: true });

// Apex Kart Model
export interface IApexKart extends Document {
  sessionId: mongoose.Types.ObjectId;
  number: number; // Align with regular Kart.number
  kartNumber: number; // Keep for compatibility
  speed?: number; // Align with regular Kart.speed
  currentTeamId?: mongoose.Types.ObjectId; // Align with regular Kart.currentTeamId (ObjectId ref)
  currentRowId?: mongoose.Types.ObjectId; // NEW - Align with regular Kart.currentRowId
  teamId: string; // Keep for compatibility
  currentDriverId: string;
  status: 'on_track' | 'in_pit_row' | 'maintenance' | 'available'; // Align with regular Kart.status enum
  lastLapTimeFormatted?: string; // For frontend display
  bestLapTimeFormatted?: string; // For frontend display
  totalLaps?: number; // For frontend display
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const ApexKartSchema = new Schema<IApexKart>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  number: { type: Number, required: true, index: true }, // Align with regular Kart.number
  kartNumber: { type: Number, required: true, index: true }, // Keep for compatibility
  speed: { type: Number }, // Align with regular Kart.speed
  currentTeamId: { type: Schema.Types.ObjectId, ref: 'ApexTeam', default: null }, // Align with regular Kart.currentTeamId
  currentRowId: { type: Schema.Types.ObjectId, ref: 'Row', default: null }, // NEW - Align with regular Kart.currentRowId
  teamId: { type: String, required: true, index: true }, // Keep for compatibility
  currentDriverId: { type: String, required: true },
  status: { // Align with regular Kart.status enum
    type: String,
    enum: ['on_track', 'in_pit_row', 'maintenance', 'available'],
    default: 'available',
    required: true,
  },
  lastLapTimeFormatted: { type: String }, // For frontend display
  bestLapTimeFormatted: { type: String }, // For frontend display
  totalLaps: { type: Number, default: 0 }, // For frontend display
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_karts'
});

// Compound index for session and kart number
ApexKartSchema.index({ sessionId: 1, kartNumber: 1 }, { unique: true });

// Apex Driver Model
export interface IApexDriver extends Document {
  sessionId: mongoose.Types.ObjectId;
  driverId: string;
  name: string;
  teamId: string;
  nationality: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const ApexDriverSchema = new Schema<IApexDriver>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  driverId: { type: String, required: true, index: true },
  name: { type: String, required: true },
  teamId: { type: String, required: true, index: true },
  nationality: { type: String, default: 'Unknown' },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_drivers'
});

// Compound index for session and driver
ApexDriverSchema.index({ sessionId: 1, driverId: 1 }, { unique: true });

// Apex Competitor Model (maps websocket IDs to team IDs)
export interface IApexCompetitor extends Document {
  sessionId: mongoose.Types.ObjectId;
  competitorId: string;
  teamId: string;
  name: string;
  kartNumber: number;
  drivers: string[];
  createdAt: Date;
  updatedAt: Date;
}

const ApexCompetitorSchema = new Schema<IApexCompetitor>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  competitorId: { type: String, required: true, index: true },
  teamId: { type: String, required: true, index: true },
  name: { type: String, required: true },
  kartNumber: { type: Number, required: true },
  drivers: [{ type: String }]
}, {
  timestamps: true,
  collection: 'apex_competitors'
});

// Compound index for session and competitor
ApexCompetitorSchema.index({ sessionId: 1, competitorId: 1 }, { unique: true });

// Apex Lap Model
export interface IApexLap extends Document {
  sessionId: mongoose.Types.ObjectId;
  competitorId: string;
  teamId: string;
  kartNumber: number;
  lapNumber: number;
  lapTime: number; // in seconds
  lapTimeFormatted: string;
  sector1?: number;
  sector2?: number;
  sector3?: number;
  isBestLap: boolean;
  isPersonalBest: boolean;
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
}

const ApexLapSchema = new Schema<IApexLap>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  competitorId: { type: String, required: true, index: true },
  teamId: { type: String, required: true, index: true },
  kartNumber: { type: Number, required: true, index: true },
  lapNumber: { type: Number, default: 0 },
  lapTime: { type: Number, required: true },
  lapTimeFormatted: { type: String, required: true },
  sector1: { type: Number },
  sector2: { type: Number },
  sector3: { type: Number },
  isBestLap: { type: Boolean, default: false },
  isPersonalBest: { type: Boolean, default: false },
  timestamp: { type: Date, required: true }
}, {
  timestamps: true,
  collection: 'apex_laps'
});

// Compound index for session, competitor and timestamp
ApexLapSchema.index({ sessionId: 1, competitorId: 1, timestamp: 1 });

// Apex Pit Stop Model
export interface IApexPitStop extends Document {
  sessionId: mongoose.Types.ObjectId;
  competitorId: string;
  teamId: string;
  kartNumber: number;
  pitInTime: Date;
  pitOutTime?: Date;
  pitDuration?: number; // in seconds
  lapNumber: number;
  reason: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const ApexPitStopSchema = new Schema<IApexPitStop>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  competitorId: { type: String, required: true, index: true },
  teamId: { type: String, required: true, index: true },
  kartNumber: { type: Number, required: true, index: true },
  pitInTime: { type: Date, required: true },
  pitOutTime: { type: Date },
  pitDuration: { type: Number },
  lapNumber: { type: Number, required: true },
  reason: { type: String, default: 'Regular' },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_pitstops'
});

// Compound index for session, competitor and pit in time
ApexPitStopSchema.index({ sessionId: 1, competitorId: 1, pitInTime: 1 });

// Apex Live Data Model (for real-time updates)
export interface IApexLiveData extends Document {
  sessionId: mongoose.Types.ObjectId;
  competitorId: string;
  position: number;
  gap: string;
  interval: string;
  lastLapTime: string;
  bestLapTime: string;
  totalLaps: number;
  sector1: string;
  sector2: string;
  sector3: string;
  status: string;
  onTrackTime: string;
  pitCount: number;
  lastUpdated: Date;
  createdAt: Date;
  updatedAt: Date;
}

const ApexLiveDataSchema = new Schema<IApexLiveData>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  competitorId: { type: String, required: true, index: true },
  position: { type: Number, default: 0 },
  gap: { type: String, default: '' },
  interval: { type: String, default: '' },
  lastLapTime: { type: String, default: '' },
  bestLapTime: { type: String, default: '' },
  totalLaps: { type: Number, default: 0 },
  sector1: { type: String, default: '' },
  sector2: { type: String, default: '' },
  sector3: { type: String, default: '' },
  status: { type: String, default: '' },
  onTrackTime: { type: String, default: '' },
  pitCount: { type: Number, default: 0 },
  lastUpdated: { type: Date, default: Date.now }
}, {
  timestamps: true,
  collection: 'apex_livedata'
});

// Compound index for session and competitor
ApexLiveDataSchema.index({ sessionId: 1, competitorId: 1 }, { unique: true });

// Export models
export const ApexSession = mongoose.model<IApexSession>('ApexSession', ApexSessionSchema);
export const ApexTeam = mongoose.model<IApexTeam>('ApexTeam', ApexTeamSchema);
export const ApexKart = mongoose.model<IApexKart>('ApexKart', ApexKartSchema);
export const ApexDriver = mongoose.model<IApexDriver>('ApexDriver', ApexDriverSchema);
export const ApexCompetitor = mongoose.model<IApexCompetitor>('ApexCompetitor', ApexCompetitorSchema);
export const ApexLap = mongoose.model<IApexLap>('ApexLap', ApexLapSchema);
export const ApexPitStop = mongoose.model<IApexPitStop>('ApexPitStop', ApexPitStopSchema);
export const ApexLiveData = mongoose.model<IApexLiveData>('ApexLiveData', ApexLiveDataSchema);
