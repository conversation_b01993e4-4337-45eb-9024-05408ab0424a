import mongoose, { Document, Schema } from 'mongoose';

// Optimized Apex Session Model
export interface IApexSession extends Document {
  title1: string;
  title2: string;
  track: string;
  isActive: boolean;
  gridData?: any; // Keep minimal grid data if needed
  sessionData?: Record<string, any>; // Keep minimal session data if needed
}

const ApexSessionSchema = new Schema<IApexSession>({
  title1: { type: String, default: '' },
  title2: { type: String, default: '' },
  track: { type: String, default: '' },
  isActive: { type: Boolean, default: true, index: true },
  gridData: { type: Schema.Types.Mixed }, // Minimal grid data
  sessionData: { type: Schema.Types.Mixed, default: {} } // Minimal session data
}, {
  timestamps: true, // Provides createdAt and updatedAt
  collection: 'apex_sessions'
});

// Optimized Apex Team Model
export interface IApexTeam extends Document {
  sessionId: mongoose.Types.ObjectId;
  name: string;
  currentKartId?: mongoose.Types.ObjectId; // Reference to current kart (ObjectId)
  pastKarts: mongoose.Types.ObjectId[]; // Array of kart ObjectIds
  pits: mongoose.Types.ObjectId[]; // Array of pit ObjectIds
  drivers: string[];
  nationality: string;
  totalLaps: number;
  bestLapTime?: number;
  bestLapTimeFormatted?: string;
  lastLapTime?: number;
  lastLapTimeFormatted?: string;
  position?: number;
  status: string;
  isActive: boolean;
}

const ApexTeamSchema = new Schema<IApexTeam>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  name: { type: String, required: true },
  currentKartId: { type: Schema.Types.ObjectId, ref: 'ApexKart', default: null },
  pastKarts: [{ type: Schema.Types.ObjectId, ref: 'ApexKart' }],
  pits: [{ type: Schema.Types.ObjectId, ref: 'Pit' }],
  drivers: [{ type: String }],
  nationality: { type: String, default: 'Unknown' },
  totalLaps: { type: Number, default: 0 },
  bestLapTime: { type: Number },
  bestLapTimeFormatted: { type: String },
  lastLapTime: { type: Number },
  lastLapTimeFormatted: { type: String },
  position: { type: Number },
  status: { type: String, default: 'active' },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_teams'
});

// Optimized Apex Kart Model
export interface IApexKart extends Document {
  sessionId: mongoose.Types.ObjectId;
  kartNumber: number; // Keep kart number for identification
  speed?: number;
  currentTeamId?: mongoose.Types.ObjectId; // Reference to current team (ObjectId)
  currentRowId?: mongoose.Types.ObjectId; // Reference to current pit row (ObjectId)
  status: 'on_track' | 'in_pit_row' | 'maintenance' | 'available';
  lastLapTimeFormatted?: string;
  bestLapTimeFormatted?: string;
  totalLaps?: number;
  isActive: boolean;
}

const ApexKartSchema = new Schema<IApexKart>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  kartNumber: { type: Number, required: true, index: true },
  speed: { type: Number, default: 3 },
  currentTeamId: { type: Schema.Types.ObjectId, ref: 'ApexTeam', default: null },
  currentRowId: { type: Schema.Types.ObjectId, ref: 'Row', default: null },
  status: {
    type: String,
    enum: ['on_track', 'in_pit_row', 'maintenance', 'available'],
    default: 'available',
    required: true,
  },
  lastLapTimeFormatted: { type: String },
  bestLapTimeFormatted: { type: String },
  totalLaps: { type: Number, default: 0 },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_karts'
});

// Compound index for session and kart number
ApexKartSchema.index({ sessionId: 1, kartNumber: 1 }, { unique: true });

// Optimized Apex Competitor Model (maps websocket IDs to team ObjectIds)
export interface IApexCompetitor extends Document {
  sessionId: mongoose.Types.ObjectId;
  competitorId: string; // Keep as string for websocket compatibility
  teamId: mongoose.Types.ObjectId; // Reference to team (ObjectId)
  kartId: mongoose.Types.ObjectId; // Reference to kart (ObjectId)
  name: string;
  nationality: string;
  drivers: string[];
  isActive: boolean;
}

const ApexCompetitorSchema = new Schema<IApexCompetitor>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  competitorId: { type: String, required: true, index: true },
  teamId: { type: Schema.Types.ObjectId, ref: 'ApexTeam', required: true, index: true },
  kartId: { type: Schema.Types.ObjectId, ref: 'ApexKart', required: true, index: true },
  name: { type: String, required: true },
  nationality: { type: String, default: 'Unknown' },
  drivers: [{ type: String }],
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_competitors'
});

// Compound index for session and competitor
ApexCompetitorSchema.index({ sessionId: 1, competitorId: 1 }, { unique: true });

// Optimized Apex Lap Model
export interface IApexLap extends Document {
  sessionId: mongoose.Types.ObjectId;
  competitorId: string; // Keep as string for websocket compatibility
  kartId: mongoose.Types.ObjectId; // Reference to kart (ObjectId)
  lapNumber: number;
  lapTime: number; // in milliseconds
  lapTimeFormatted: string;
  sector1?: number;
  sector2?: number;
  sector3?: number;
  isBestLap: boolean;
  isPersonalBest: boolean;
  timestamp: Date;
}

const ApexLapSchema = new Schema<IApexLap>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  competitorId: { type: String, required: true, index: true },
  kartId: { type: Schema.Types.ObjectId, ref: 'ApexKart', required: true, index: true },
  lapNumber: { type: Number, default: 0 },
  lapTime: { type: Number, required: true }, // milliseconds
  lapTimeFormatted: { type: String, required: true },
  sector1: { type: Number },
  sector2: { type: Number },
  sector3: { type: Number },
  isBestLap: { type: Boolean, default: false },
  isPersonalBest: { type: Boolean, default: false },
  timestamp: { type: Date, required: true }
}, {
  timestamps: true, // Only createdAt, no updatedAt needed for laps
  collection: 'apex_laps'
});

// Compound index for session, competitor and timestamp
ApexLapSchema.index({ sessionId: 1, competitorId: 1, timestamp: 1 });

// Optimized Apex Pit Stop Model
export interface IApexPitStop extends Document {
  sessionId: mongoose.Types.ObjectId;
  competitorId: string; // Keep as string for websocket compatibility
  kartId: mongoose.Types.ObjectId; // Reference to kart (ObjectId)
  pitInTime: Date;
  pitOutTime?: Date;
  pitDuration?: number; // in seconds
  lapNumber: number;
  reason: string;
  isActive: boolean;
}

const ApexPitStopSchema = new Schema<IApexPitStop>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  competitorId: { type: String, required: true, index: true },
  kartId: { type: Schema.Types.ObjectId, ref: 'ApexKart', required: true, index: true },
  pitInTime: { type: Date, required: true },
  pitOutTime: { type: Date },
  pitDuration: { type: Number },
  lapNumber: { type: Number, required: true },
  reason: { type: String, default: 'Regular' },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_pitstops'
});

// Compound index for session, competitor and pit in time
ApexPitStopSchema.index({ sessionId: 1, competitorId: 1, pitInTime: 1 });

// Apex Live Data Model (for real-time updates)
export interface IApexLiveData extends Document {
  sessionId: mongoose.Types.ObjectId;
  competitorId: string;
  position: number;
  gap: string;
  interval: string;
  lastLapTime: string;
  bestLapTime: string;
  totalLaps: number;
  sector1: string;
  sector2: string;
  sector3: string;
  status: string;
  onTrackTime: string;
  pitCount: number;
  lastUpdated: Date;
  createdAt: Date;
  updatedAt: Date;
}

const ApexLiveDataSchema = new Schema<IApexLiveData>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  competitorId: { type: String, required: true, index: true },
  position: { type: Number, default: 0 },
  gap: { type: String, default: '' },
  interval: { type: String, default: '' },
  lastLapTime: { type: String, default: '' },
  bestLapTime: { type: String, default: '' },
  totalLaps: { type: Number, default: 0 },
  sector1: { type: String, default: '' },
  sector2: { type: String, default: '' },
  sector3: { type: String, default: '' },
  status: { type: String, default: '' },
  onTrackTime: { type: String, default: '' },
  pitCount: { type: Number, default: 0 },
  lastUpdated: { type: Date, default: Date.now }
}, {
  timestamps: true,
  collection: 'apex_livedata'
});

// Compound index for session and competitor
ApexLiveDataSchema.index({ sessionId: 1, competitorId: 1 }, { unique: true });

// Export optimized models
export const ApexSession = mongoose.model<IApexSession>('ApexSession', ApexSessionSchema);
export const ApexTeam = mongoose.model<IApexTeam>('ApexTeam', ApexTeamSchema);
export const ApexKart = mongoose.model<IApexKart>('ApexKart', ApexKartSchema);
export const ApexCompetitor = mongoose.model<IApexCompetitor>('ApexCompetitor', ApexCompetitorSchema);
export const ApexLap = mongoose.model<IApexLap>('ApexLap', ApexLapSchema);
export const ApexPitStop = mongoose.model<IApexPitStop>('ApexPitStop', ApexPitStopSchema);
export const ApexLiveData = mongoose.model<IApexLiveData>('ApexLiveData', ApexLiveDataSchema);
