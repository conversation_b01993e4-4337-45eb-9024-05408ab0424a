import { EventEmitter } from 'events';
import mongoose from 'mongoose';
import { parseLogMessage, ParsedMessage } from './apexMessageParser';
import { parseGridData, GridParseResult } from './apexGridParser';
import { ApexSession, ApexTeam, ApexKart, ApexCompetitor, ApexLap } from '../models/ApexModels';

export interface ApexParserConfig {
  sessionId?: string;
  raceId?: string;
  enableLogging?: boolean;
}

/**
 * Simplified Apex Parser
 * 
 * Logic:
 * 1. Parse socket message
 * 2. If grid is found -> parse and create database elements
 * 3. If grid not found -> update existing database elements for old parsed grid
 * 4. Parse all received lines
 */
export class ApexParserSimple extends EventEmitter {
  private currentSession: any = null;
  private gridData: GridParseResult | null = null;
  private config: ApexParserConfig;

  constructor(config: ApexParserConfig = {}) {
    super();
    this.config = {
      enableLogging: true,
      ...config
    };
  }

  /**
   * Parse log content (can be single line or multi-line)
   */
  async parseLogContent(content: string): Promise<void> {
    try {
      // Split content into lines and process each line
      const lines = content.split('\n').filter(line => line.trim().length > 0);
      
      if (this.config.enableLogging) {
        console.log(`📝 Processing ${lines.length} lines`);
      }

      for (const line of lines) {
        await this.parseMessage(line);
      }
    } catch (error) {
      console.error('Error parsing log content:', error);
      throw error;
    }
  }

  /**
   * Parse a single websocket message
   */
  async parseMessage(rawMessage: string): Promise<void> {
    try {
      if (this.config.enableLogging) {
        console.log('📨 Parsing message:', rawMessage.substring(0, 100) + '...');
      }

      // Parse the message
      const message = parseLogMessage(rawMessage);
      
      if (!message || !message.data) {
        if (this.config.enableLogging) {
          console.warn('⚠️ Unknown message format, logging original message:');
          console.warn('📝 Original message:', rawMessage);
        }
        return;
      }

      // Emit raw message event
      this.emit('messageReceived', { raw: rawMessage, parsed: message });

      // Simple logic: check if grid is present
      if (message.data.grid) {
        // Grid found -> parse and create database elements
        if (this.config.enableLogging) {
          console.log('🏁 Grid found - creating database elements');
        }
        await this.handleGridMessage(message.data);
      } else {
        // No grid -> update existing database elements
        if (this.config.enableLogging) {
          console.log(`🔄 No grid - updating existing elements (${Object.keys(message.data).length} fields)`);
        }
        await this.handleUpdateMessage(message.data);
      }
    } catch (error) {
      console.error('Error parsing message:', error);
      console.error('📝 Original message that caused error:', rawMessage);
    }
  }

  /**
   * Handle message with grid data - create database elements
   */
  private async handleGridMessage(messageData: Record<string, any>): Promise<void> {
    try {
      // Parse grid data
      if (messageData.grid) {
        if (this.config.enableLogging) {
          console.log('📊 Parsing grid data...');
        }
        this.gridData = parseGridData(messageData.grid.value);
        if (this.config.enableLogging) {
          console.log(`   Parsed ${Object.keys(this.gridData.drivers).length} drivers from grid`);
        }
      }

      // Create or update session
      await this.createSession(messageData);

      // Create teams, karts, and competitors from grid data
      if (this.gridData && this.currentSession) {
        await this.createEntitiesFromGrid();
      }

    } catch (error) {
      console.error('Error handling grid message:', error);
    }
  }

  /**
   * Handle message without grid data - update existing database elements
   */
  private async handleUpdateMessage(messageData: Record<string, any>): Promise<void> {
    try {
      // Skip updates if no session with grid elements exists yet
      if (!this.currentSession || !this.gridData) {
        if (this.config.enableLogging) {
          console.log('⚠️ Skipping updates - no session with grid elements found yet. Waiting for init message with grid.');
        }
        return;
      }

      // Process all fields as updates
      await this.processUpdates(messageData);

    } catch (error) {
      console.error('Error handling update message:', error);
    }
  }

  /**
   * Create session from message data
   */
  private async createSession(messageData: Record<string, any>): Promise<void> {
    try {
      const now = new Date();
      const sessionData = {
        raceId: this.config.raceId || `race_${Date.now()}`, // Required field
        title1: messageData.title1?.value || 'Race Session',
        title2: messageData.title2?.value || now.toISOString(),
        track: messageData.track?.value || 'Unknown Track',
        startTime: now, // Required field
        isActive: true,
        gridData: this.gridData || {},
        sessionData: messageData || {},
        lastUpdated: now,
        createdAt: now,
        updatedAt: now
      };

      // Check if session already exists
      if (this.config.sessionId) {
        this.currentSession = await ApexSession.findById(this.config.sessionId);
      }

      if (!this.currentSession) {
        this.currentSession = await ApexSession.create(sessionData);
        if (this.config.enableLogging) {
          console.log(`✅ Created session: ${this.currentSession._id} (raceId: ${sessionData.raceId})`);
        }
      } else {
        await ApexSession.findByIdAndUpdate(this.currentSession._id, {
          ...sessionData,
          updatedAt: new Date()
        });
        if (this.config.enableLogging) {
          console.log(`✅ Updated session: ${this.currentSession._id}`);
        }
      }

      this.emit('sessionCreated', this.currentSession);
    } catch (error) {
      console.error('Error creating session:', error);
    }
  }

  /**
   * Create default session when none exists
   */
  private async createDefaultSession(): Promise<void> {
    try {
      const now = new Date();
      const sessionData = {
        raceId: this.config.raceId || `default_race_${Date.now()}`, // Required field
        title1: 'Default Session',
        title2: now.toISOString(),
        track: 'Unknown Track',
        startTime: now, // Required field
        isActive: true,
        gridData: {},
        sessionData: {},
        lastUpdated: now,
        createdAt: now,
        updatedAt: now
      };

      this.currentSession = await ApexSession.create(sessionData);
      if (this.config.enableLogging) {
        console.log(`✅ Created default session: ${this.currentSession._id} (raceId: ${sessionData.raceId})`);
      }
    } catch (error) {
      console.error('Error creating default session:', error);
    }
  }

  /**
   * Create teams, karts, and competitors from grid data
   */
  private async createEntitiesFromGrid(): Promise<void> {
    if (!this.gridData || !this.currentSession) return;

    try {
      const sessionId = this.currentSession._id;

      // Check for existing entities to avoid duplicates
      const existingCompetitors = await ApexCompetitor.find({ sessionId }).lean();
      const existingTeams = await ApexTeam.find({ sessionId }).lean();
      const existingKarts = await ApexKart.find({ sessionId }).lean();

      const existingCompetitorIds = new Set(existingCompetitors.map(c => c.competitorId));
      const existingTeamIds = new Set(existingTeams.map(t => t.teamId));
      const existingKartNumbers = new Set(existingKarts.map(k => k.kartNumber));

      const teamsToCreate = [];
      const kartsToCreate = [];
      const competitorsToCreate = [];

      // Process each driver from grid
      for (const [driverId, driverData] of Object.entries(this.gridData.drivers)) {
        const competitorId = driverId.replace('r', '');
        const teamName = driverData.dr?.value || `Team ${competitorId}`;
        const kartNumber = parseInt(driverData.no?.value || '0') || 0;
        const nationality = driverData.nat?.value || '';

        if (kartNumber > 0) {
          // Create competitor (if not exists)
          if (!existingCompetitorIds.has(competitorId)) {
            competitorsToCreate.push({
              sessionId,
              competitorId,
              name: teamName,
              kartNumber,
              nationality,
              teamId: competitorId,
              createdAt: new Date(),
              updatedAt: new Date()
            });
          }

          // Create team (if not exists)
          if (!existingTeamIds.has(competitorId)) {
            teamsToCreate.push({
              sessionId,
              teamId: competitorId,
              name: teamName,
              number: kartNumber,
              kartNumber,
              currentKartId: null,
              pastKarts: [],
              pits: [],
              createdAt: new Date(),
              updatedAt: new Date()
            });
          }

          // Create kart (if not exists)
          if (!existingKartNumbers.has(kartNumber)) {
            kartsToCreate.push({
              sessionId,
              number: kartNumber,
              kartNumber,
              speed: 3, // Default speed
              teamId: competitorId,
              currentTeamId: null,
              currentRowId: null,
              status: 'on_track',
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            });
          }
        }
      }

      // Bulk create entities (only new ones)
      if (competitorsToCreate.length > 0) {
        await ApexCompetitor.insertMany(competitorsToCreate);
        if (this.config.enableLogging) {
          console.log(`✅ Created ${competitorsToCreate.length} new competitors`);
        }
      } else if (this.config.enableLogging) {
        console.log('✅ All competitors already exist');
      }

      if (teamsToCreate.length > 0) {
        await ApexTeam.insertMany(teamsToCreate);
        if (this.config.enableLogging) {
          console.log(`✅ Created ${teamsToCreate.length} new teams`);
        }
      } else if (this.config.enableLogging) {
        console.log('✅ All teams already exist');
      }

      if (kartsToCreate.length > 0) {
        await ApexKart.insertMany(kartsToCreate);
        if (this.config.enableLogging) {
          console.log(`✅ Created ${kartsToCreate.length} new karts`);
        }
      } else if (this.config.enableLogging) {
        console.log('✅ All karts already exist');
      }

      // Create apex karts for pit rows
      try {
        const { ApexPitRowService } = await import('./apexPitRowService');
        await ApexPitRowService.createApexKartsForPitRows(sessionId.toString());
        if (this.config.enableLogging) {
          console.log('✅ Created apex karts for pit rows');
        }
      } catch (pitRowError) {
        console.error('Error creating apex karts for pit rows:', pitRowError);
      }

    } catch (error) {
      console.error('Error creating entities from grid:', error);
    }
  }

  /**
   * Process update messages for existing entities
   */
  private async processUpdates(messageData: Record<string, any>): Promise<void> {
    if (!this.currentSession) return;

    try {
      for (const [key, data] of Object.entries(messageData)) {
        // Convert data to expected format if needed
        const fieldData = typeof data === 'object' && data.value !== undefined 
          ? data 
          : { type: key, value: String(data) };

        if (key.startsWith('r') && key.includes('c')) {
          // Driver/competitor field (format: r{id}c{column})
          await this.handleDriverUpdate(key, fieldData);
        } else {
          // Session field or other
          await this.handleSessionUpdate(key, fieldData);
        }
      }
    } catch (error) {
      console.error('Error processing updates:', error);
    }
  }

  /**
   * Handle driver/competitor updates
   */
  private async handleDriverUpdate(key: string, data: any): Promise<void> {
    // Extract competitor ID and column from key (e.g., "r45393c4" -> competitorId: "45393", column: "c4")
    const match = key.match(/r(\d+)c(\d+)/);
    if (!match) return;

    const competitorId = match[1];
    const columnId = `c${match[2]}`;

    // Get the field type from grid header if available
    const fieldType = this.gridData?.header_types[columnId] || 'unknown';

    if (this.config.enableLogging) {
      console.log(`Updating competitor ${competitorId}, field ${fieldType}: ${data.value}`);
    }

    // Handle different field types
    switch (fieldType) {
      case 'llp': // Last lap time
      case 'blp': // Best lap time
        if (competitorId && data.value) {
          await this.handleLapTimeUpdate(competitorId, fieldType, data.value);
        }
        break;
      case 'sta': // Status
        if (competitorId && data.value) {
          await this.handleStatusUpdate(competitorId, data.value);
        }
        break;
      case 'pit': // Pit status
        if (competitorId && data.value) {
          await this.handlePitUpdate(competitorId, data.value);
        }
        break;
      default:
        // Generic field update
        break;
    }
  }

  /**
   * Handle session-level updates
   */
  private async handleSessionUpdate(key: string, data: any): Promise<void> {
    if (!this.currentSession) return;

    try {
      await ApexSession.findByIdAndUpdate(this.currentSession._id, {
        [key]: data.value,
        updatedAt: new Date()
      });
    } catch (error) {
      console.error(`Error updating session field ${key}:`, error);
    }
  }

  /**
   * Handle lap time updates
   */
  private async handleLapTimeUpdate(competitorId: string, fieldType: string, lapTime: string): Promise<void> {
    if (!lapTime || lapTime === '') return;

    try {
      // Find competitor to get team info
      const competitor = await ApexCompetitor.findOne({
        sessionId: this.currentSession._id,
        competitorId
      });

      if (!competitor) return;

      // Create lap record
      const lapData = {
        sessionId: this.currentSession._id,
        teamId: competitor.teamId,
        kartNumber: competitor.kartNumber,
        lapTime,
        lapTimeFormatted: lapTime,
        timestamp: new Date()
      };

      await ApexLap.create(lapData);
      
      if (this.config.enableLogging) {
        console.log(`📊 Lap time recorded: ${competitor.name} - ${lapTime}`);
      }
    } catch (error) {
      console.error('Error handling lap time update:', error);
    }
  }

  /**
   * Handle status updates
   */
  private async handleStatusUpdate(competitorId: string, status: string): Promise<void> {
    try {
      await ApexKart.updateOne(
        {
          sessionId: this.currentSession._id,
          teamId: competitorId
        },
        {
          status: status === 'PIT' ? 'in_pit' : 'on_track',
          updatedAt: new Date()
        }
      );
    } catch (error) {
      console.error('Error handling status update:', error);
    }
  }

  /**
   * Handle pit updates
   */
  private async handlePitUpdate(competitorId: string, pitStatus: string): Promise<void> {
    // Similar to status update but specifically for pit events
    await this.handleStatusUpdate(competitorId, pitStatus);
  }

  /**
   * Get current session
   */
  getCurrentSession() {
    return this.currentSession;
  }

  /**
   * Get grid data
   */
  getGridData() {
    return this.gridData;
  }
}
