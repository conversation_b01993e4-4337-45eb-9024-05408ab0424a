import { EventEmitter } from 'events';
import mongoose from 'mongoose';
import ApexParser from './apexParser';
import { sendWebSocketMessage } from '../websocket/websocket';

export interface RealtimeConfig {
  sessionId?: string | mongoose.Types.ObjectId;
  raceId?: string;
  enableLogging?: boolean;
  autoCreateSession?: boolean;
  broadcastUpdates?: boolean;
}

export class ApexRealtimeReceiver extends EventEmitter {
  private parser: ApexParser;
  private config: RealtimeConfig;
  private isActive: boolean = false;
  private messageCount: number = 0;
  private lastMessageTime: Date | null = null;

  constructor(config: RealtimeConfig = {}) {
    super();
    
    this.config = {
      enableLogging: true,
      autoCreateSession: true,
      broadcastUpdates: true,
      ...config
    };

    this.parser = new ApexParser({
      sessionId: this.config.sessionId,
      raceId: this.config.raceId,
      enableLogging: this.config.enableLogging
    });

    this.setupParserEvents();

    // Initialize with existing session if sessionId is provided
    if (this.config.sessionId) {
      this.initializeWithExistingSession();
    }
  }

  /**
   * Initialize with existing session
   */
  private async initializeWithExistingSession(): Promise<void> {
    try {
      await this.parser.initializeWithExistingSession();
    } catch (error) {
      if (this.config.enableLogging) {
        console.error('Error initializing with existing session:', error);
      }
    }
  }

  /**
   * Setup event listeners for the parser
   */
  private setupParserEvents(): void {
    // Forward parser events
    this.parser.on('sessionCreated', (session) => {
      this.emit('sessionCreated', session);
      if (this.config.broadcastUpdates) {
        sendWebSocketMessage({
          type: 'apex_session_created',
          session: {
            sessionId: session._id,
            raceId: session.raceId,
            title1: session.title1,
            title2: session.title2,
            track: session.track
          }
        });
      }
    });

    this.parser.on('teamCreated', (team) => {
      this.emit('teamCreated', team);
      if (this.config.broadcastUpdates) {
        sendWebSocketMessage({
          type: 'apex_team_created',
          team: {
            teamId: team.teamId,
            name: team.name,
            kartNumber: team.kartNumber,
            drivers: team.drivers
          }
        });
      }
    });

    this.parser.on('lapRecorded', (lap) => {
      this.emit('lapRecorded', lap);
      if (this.config.broadcastUpdates) {
        sendWebSocketMessage({
          type: 'apex_lap_recorded',
          lap: {
            competitorId: lap.competitorId,
            lapTime: lap.lapTime,
            lapTimeFormatted: lap.lapTimeFormatted,
            isBestLap: lap.isBestLap,
            timestamp: lap.timestamp
          }
        });
      }
    });

    this.parser.on('pitStopRecorded', (pitStop) => {
      this.emit('pitStopRecorded', pitStop);
      if (this.config.broadcastUpdates) {
        sendWebSocketMessage({
          type: 'apex_pitstop_recorded',
          pitStop: {
            competitorId: pitStop.competitorId,
            pitInTime: pitStop.pitInTime,
            pitOutTime: pitStop.pitOutTime,
            pitDuration: pitStop.pitDuration
          }
        });
      }
    });

    this.parser.on('positionUpdate', (update) => {
      this.emit('positionUpdate', update);
      if (this.config.broadcastUpdates) {
        sendWebSocketMessage({
          type: 'apex_position_update',
          update
        });
      }
    });

    this.parser.on('error', (error) => {
      this.emit('error', error);
      if (this.config.enableLogging) {
        console.error('Parser error:', error);
      }
    });
  }

  /**
   * Start receiving real-time data
   */
  start(): void {
    if (this.isActive) {
      console.log('Realtime receiver already active');
      return;
    }

    this.isActive = true;
    this.messageCount = 0;
    this.lastMessageTime = null;

    if (this.config.enableLogging) {
      console.log('🔴 Apex Realtime Receiver started');
    }

    this.emit('started');
  }

  /**
   * Stop receiving real-time data
   */
  stop(): void {
    if (!this.isActive) {
      return;
    }

    this.isActive = false;

    if (this.config.enableLogging) {
      console.log('⏹️ Apex Realtime Receiver stopped');
      console.log(`📊 Processed ${this.messageCount} messages`);
    }

    this.emit('stopped', {
      messageCount: this.messageCount,
      lastMessageTime: this.lastMessageTime
    });
  }

  /**
   * Process incoming websocket message
   */
  async processMessage(messageContent: string): Promise<void> {
    if (!this.isActive) {
      if (this.config.enableLogging) {
        console.warn('Received message while receiver is inactive');
      }
      return;
    }

    try {
      this.messageCount++;
      this.lastMessageTime = new Date();

      // Parse the message using the Apex parser
      await this.parser.parseLogLine(messageContent);

      // Emit message processed event
      this.emit('messageProcessed', {
        messageCount: this.messageCount,
        content: messageContent.substring(0, 100) + '...',
        timestamp: this.lastMessageTime
      });

      if (this.config.enableLogging && this.messageCount % 10 === 0) {
        console.log(`📨 Processed ${this.messageCount} messages`);
      }

    } catch (error) {
      console.error('Error processing message:', error);
      this.emit('error', {
        error,
        messageContent,
        messageCount: this.messageCount
      });
    }
  }

  /**
   * Get current session from parser
   */
  getCurrentSession() {
    return this.parser.getCurrentSession();
  }

  /**
   * Get grid data from parser
   */
  getGridData() {
    return this.parser.getGridData();
  }

  /**
   * Get receiver statistics
   */
  getStats() {
    return {
      isActive: this.isActive,
      messageCount: this.messageCount,
      lastMessageTime: this.lastMessageTime,
      currentSession: this.getCurrentSession() ? {
        sessionId: this.getCurrentSession()._id,
        raceId: this.getCurrentSession().raceId,
        title1: this.getCurrentSession().title1,
        title2: this.getCurrentSession().title2
      } : null
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<RealtimeConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Update parser config if needed
    if (newConfig.sessionId || newConfig.raceId || newConfig.enableLogging !== undefined) {
      // Create new parser with updated config
      this.parser = new ApexParser({
        sessionId: this.config.sessionId,
        raceId: this.config.raceId,
        enableLogging: this.config.enableLogging
      });
      this.setupParserEvents();
    }

    this.emit('configUpdated', this.config);
  }

  /**
   * Get current configuration
   */
  getConfig(): RealtimeConfig {
    return { ...this.config };
  }

  /**
   * Check if receiver is active
   */
  isReceiving(): boolean {
    return this.isActive;
  }

  /**
   * Reset receiver state
   */
  reset(): void {
    this.stop();
    this.messageCount = 0;
    this.lastMessageTime = null;
    
    // Create new parser instance
    this.parser = new ApexParser({
      sessionId: this.config.sessionId,
      raceId: this.config.raceId,
      enableLogging: this.config.enableLogging
    });
    this.setupParserEvents();

    this.emit('reset');
  }

  /**
   * Process batch of messages
   */
  async processBatch(messages: string[]): Promise<void> {
    if (!this.isActive) {
      throw new Error('Receiver is not active');
    }

    const startTime = Date.now();
    let processedCount = 0;
    let errorCount = 0;

    for (const message of messages) {
      try {
        await this.processMessage(message);
        processedCount++;
      } catch (error) {
        errorCount++;
        console.error('Error in batch processing:', error);
      }
    }

    const duration = Date.now() - startTime;

    this.emit('batchProcessed', {
      totalMessages: messages.length,
      processedCount,
      errorCount,
      duration,
      messagesPerSecond: processedCount / (duration / 1000)
    });

    if (this.config.enableLogging) {
      console.log(`📦 Batch processed: ${processedCount}/${messages.length} messages in ${duration}ms`);
    }
  }
}

// Global instance for the realtime receiver
let globalReceiver: ApexRealtimeReceiver | null = null;

/**
 * Get or create the global realtime receiver instance
 */
export const getRealtimeReceiver = (config?: RealtimeConfig): ApexRealtimeReceiver => {
  if (!globalReceiver) {
    globalReceiver = new ApexRealtimeReceiver(config);
  }
  return globalReceiver;
};

/**
 * Initialize the realtime receiver with websocket integration
 */
export const initializeRealtimeReceiver = (config?: RealtimeConfig): ApexRealtimeReceiver => {
  const receiver = getRealtimeReceiver(config);
  
  // Start the receiver
  receiver.start();
  
  if (config?.enableLogging !== false) {
    console.log('🚀 Apex Realtime Receiver initialized and started');
  }
  
  return receiver;
};

export default ApexRealtimeReceiver;
