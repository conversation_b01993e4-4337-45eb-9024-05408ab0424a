#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to clear apex collection data to avoid schema conflicts
 * This allows the optimized schema to start fresh
 */

const mongoose = require('mongoose');

async function clearApexData() {
  console.log('🧹 Clearing apex collection data for fresh start...');
  
  try {
    // Use the same connection string as the application
    const mongoUri = process.env.MONGO_URI || process.env.MONGODB_URI || 'mongodb://localhost:27017/race-planner';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to database');
    
    const db = mongoose.connection.db;
    
    // Clear apex collections to avoid schema conflicts
    const collections = [
      'apex_sessions',
      'apex_teams', 
      'apex_karts',
      'apex_competitors',
      'apex_laps',
      'apex_pitstops'
    ];
    
    for (const collectionName of collections) {
      try {
        const collection = db.collection(collectionName);
        const count = await collection.countDocuments();
        
        if (count > 0) {
          await collection.deleteMany({});
          console.log(`🗑️ Cleared ${count} documents from ${collectionName}`);
        } else {
          console.log(`ℹ️ Collection ${collectionName} is already empty`);
        }
        
        // Drop all indexes to avoid conflicts
        try {
          await collection.dropIndexes();
          console.log(`🗑️ Dropped indexes from ${collectionName}`);
        } catch (e) {
          console.log(`ℹ️ No indexes to drop from ${collectionName}`);
        }
        
      } catch (error) {
        console.log(`⚠️ Error clearing ${collectionName}:`, error.message);
      }
    }
    
    console.log('\n🎉 Apex data cleanup completed!');
    console.log('ℹ️ The optimized schema will create fresh data on next run');
    
  } catch (error) {
    console.error('❌ Data cleanup failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from database');
  }
}

// Run the cleanup
if (require.main === module) {
  clearApexData().catch(console.error);
}

module.exports = { clearApexData };
